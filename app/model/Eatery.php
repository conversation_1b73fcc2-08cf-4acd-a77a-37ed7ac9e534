<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\Eatery
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property \app\model\User[] $likes
 * @property int $cost
 * @property int $id
 * @property int $ord 排序值
 * @property int $rec 推荐值
 * @property int $status
 * @property int $views
 * @property int $can_reserve 是否支持预订：0=不支持，1=支持
 * @property mixed $hours
 * @property mixed $images
 * @property mixed $location
 * @property mixed $tags
 * @property string $address
 * @property string $name
 * @property string $phone 联系方式
 * @property int $type 商铺类型
 * @property-read \app\model\Food[] $foods
 * @property-read \app\model\UserCoupon[] $coupons
 * @property-read \app\model\User[] $members
 * @property-read \app\model\User $owner
 * @property-read \app\model\User[] $stars
 * @property-read \app\model\Reservation[] $reservations
 */
class Eatery extends Model
{
    // 商家状态常量
    const STATUS_PENDING  = 0;  // 待审核
    const STATUS_APPROVED = 1; // 已通过
    const STATUS_REJECTED = 2; // 已拒绝
    const STATUS_DISABLED = 3; // 已禁用

    // 商铺类型常量
    const TYPE_UNDEFINED = 0; // 未设置
    const TYPE_FOOD      = 1;      // 美食
    const TYPE_SPECIALTY = 2;  // 特产

    protected $json      = ['images', 'hours', 'location', 'tags'];
    protected $jsonAssoc = true;

    public function members()
    {
        return $this->belongsToMany(User::class, EateryMember::class, 'user_id', 'eatery_id');
    }

    protected function getOwnerAttr()
    {
        return $this->members()->wherePivot('access_level', EateryMember::OWNER)->find();
    }

    public function coupons()
    {
        return $this->hasMany(UserCoupon::class, 'eatery_id', 'id');
    }

    public function stars()
    {
        return $this->belongsToMany(User::class, EateryStar::class, 'user_id', 'eatery_id');
    }

    public function likes()
    {
        return $this->belongsToMany(User::class, EateryLike::class, 'user_id', 'eatery_id');
    }

    public function foods()
    {
        return $this->hasMany(Food::class, 'eatery_id', 'id');
    }

    /**
     * 获取餐厅的预订记录
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class, 'eatery_id', 'id');
    }

    protected function getCanReserveAttr($value)
    {
        if ($this->getAttr('type') == self::TYPE_SPECIALTY) {
            return 0;
        }
        return $value;
    }
}
