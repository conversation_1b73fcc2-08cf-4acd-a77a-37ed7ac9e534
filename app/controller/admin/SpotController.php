<?php

namespace app\controller\admin;

use app\model\OperationLog;
use app\model\Spot;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\annotation\route\Delete;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\exception\ValidateException;
use think\facade\Filesystem;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class SpotController extends Controller
{
    /**
     * 获取景点列表（已审核通过的景点）
     */
    #[Get('spot')]
    public function index()
    {
        $query = Spot::where('status', Spot::STATUS_NORMAL)
            ->append(['owner'])
            ->order('id', 'desc');

        $this->searchField($query, 'name');

        return $query->paginate();
    }

    /**
     * 获取待审核的景点列表
     */
    #[Get('spot/pending')]
    public function pending()
    {
        return Spot::where('status', Spot::STATUS_PENDING)
            ->with(['owner'])
            ->order('id', 'desc')
            ->paginate();
    }

    /**
     * 创建景点
     */
    #[Post('spot')]
    public function save()
    {
        $data = $this->validateSpotData();

        $spot = Spot::create($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_SPOT,
            "创建景点 {$data['name']}",
            $spot->id,
            'spot'
        );

        return $spot;
    }

    /**
     * 获取景点详情
     */
    #[Get('spot/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        // 获取景点信息，包括关联的用户信息
        $spot = Spot::with(['members'])->findOrFail($id);

        return $spot;
    }

    /**
     * 审核景点（通过）
     */
    #[Post('spot/:id/approve')]
    #[Pattern('id', '\d+')]
    public function approve($id)
    {
        $spot = Spot::where('status', Spot::STATUS_PENDING)->findOrFail($id);

        $spot->save(['status' => Spot::STATUS_NORMAL]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_APPROVE,
            OperationLog::MODULE_SPOT,
            "审核通过景点",
            $spot->id,
            'spot'
        );

        return json(['message' => '审核通过']);
    }

    /**
     * 审核景点（拒绝）
     */
    #[Post('spot/:id/reject')]
    #[Pattern('id', '\d+')]
    public function reject($id)
    {
        $spot = Spot::where('status', Spot::STATUS_PENDING)->findOrFail($id);

        $spot->save(['status' => Spot::STATUS_REJECTED]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_REJECT,
            OperationLog::MODULE_SPOT,
            "拒绝景点审核",
            $spot->id,
            'spot'
        );

        return json(['message' => '已拒绝']);
    }

    /**
     * 更新景点信息
     */
    #[Put('spot/:id')]
    #[Pattern('id', '\d+')]
    public function update($id)
    {
        $spot = Spot::findOrFail($id);

        $data = $this->validateSpotData();

        $spot->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_SPOT,
            "更新景点 {$spot->name}",
            $spot->id,
            'spot'
        );

        return $spot;
    }

    /**
     * 获取景点的成员列表
     */
    #[Get('spot/:id/members')]
    #[Pattern('id', '\d+')]
    public function members($id)
    {
        // 验证景点是否存在
        $spot = Spot::findOrFail($id);

        // 获取景点的所有成员（包括所有者和普通成员）
        $members = $spot->members()
            ->order('pivot.access_level', 'desc')
            ->order('pivot.create_time', 'asc')
            ->select();

        // 为每个成员添加角色信息
        $members->each(function ($member) {
            $member->role = $member->pivot->access_level == \app\model\SpotMember::OWNER ? '所有者' : '成员';
        });

        return $members;
    }

    /**
     * 删除景点
     */
    #[Delete('spot/:id')]
    #[Pattern('id', '\d+')]
    public function delete($id)
    {
        $spot = Spot::findOrFail($id);

        $spot->delete();

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_DELETE,
            OperationLog::MODULE_SPOT,
            "删除景点",
            $id,
            'spot'
        );
    }

    /**
     * 更新景点的推荐值
     */
    #[Put('spot/:id/rec')]
    #[Pattern('id', '\d+')]
    public function updateRec($id)
    {
        // 验证景点是否存在
        $spot = Spot::findOrFail($id);

        // 验证提交的数据
        $data = $this->validate([
            'rec|推荐值' => 'require|integer',
        ]);

        // 更新景点信息
        $spot->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_SPOT,
            "更新景点推荐值",
            $spot->id,
            'spot'
        );

        return $spot;
    }

    /**
     * 更新景点的排序值
     */
    #[Put('spot/:id/ord')]
    #[Pattern('id', '\d+')]
    public function updateOrd($id)
    {
        // 验证景点是否存在
        $spot = Spot::findOrFail($id);

        // 验证提交的数据
        $data = $this->validate([
            'ord|排序值' => 'require|integer',
        ]);

        // 更新景点信息
        $spot->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_SPOT,
            "更新景点排序值",
            $spot->id,
            'spot'
        );

        return $spot;
    }

    /**
     * 禁用景点
     */
    #[Put('spot/:id/disable')]
    #[Pattern('id', '\d+')]
    public function disable($id)
    {
        $spot = Spot::findOrFail($id);

        // 设置状态为禁用
        $spot->save(['status' => Spot::STATUS_DISABLED]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_SPOT,
            "禁用景点",
            $spot->id,
            'spot'
        );

        return $spot;
    }

    /**
     * 启用景点
     */
    #[Put('spot/:id/enable')]
    #[Pattern('id', '\d+')]
    public function enable($id)
    {
        $spot = Spot::findOrFail($id);

        // 设置状态为正常
        $spot->save(['status' => Spot::STATUS_NORMAL]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_SPOT,
            "启用景点",
            $spot->id,
            'spot'
        );

        return $spot;
    }

    /**
     * 导出景点数据
     */
    #[Post('spot/export')]
    public function export()
    {
        // 获取所有景点数据（不分页）
        $spots = Spot::order('id', 'desc')->select();

        // 检查是否有景点数据
        if ($spots->isEmpty()) {
            throw new ValidateException('没有景点数据可导出');
        }

        // 生成文件名
        $baseFilename = '景点数据_' . date('YmdHis');

        // 创建导出目录
        $disk = Filesystem::disk('uploads');
        $exportDir = 'exports';

        // 创建Excel文件
        $excelFilename = $baseFilename . '.xlsx';
        $excelPath = $exportDir . '/' . $excelFilename;
        $excelFullPath = root_path() . 'storage/uploads/' . $excelPath;

        // 确保目录存在
        $excelDir = dirname($excelFullPath);
        if (!is_dir($excelDir)) {
            mkdir($excelDir, 0755, true);
        }

        // 创建Spreadsheet对象
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('景点数据');

        // 设置表头
        $headers = ['ID', '景点名称', '商家类型', '开放时间', '区县', '位置', '详细地址', '联系电话', '收藏', '点赞', '浏览', '推荐值', '排序值'];
        $sheet->fromArray($headers, null, 'A1');

        // 设置表头样式
        $headerStyle = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'DDDDDD',
                ],
            ],
        ];

        $sheet->getStyle('A1:M1')->applyFromArray($headerStyle);

        // 准备数据
        $data = [];
        foreach ($spots as $spot) {
            // 从location中提取位置信息
            $locationName = $spot->location ? ($spot->location['name'] ?? '') : '';
            $district = $spot->location ? ($spot->location['district'] ?? '') : '';

            // 格式化开放时间
            $hours = '';
            if ($spot->hours) {
                $start = $spot->hours['start'] ?? '';
                $end = $spot->hours['end'] ?? '';
                if ($start && $end) {
                    $hours = $start . '-' . $end;
                }
            }

            $data[] = [
                $spot->id,
                $spot->name,
                '景点',  // 商家类型固定为景点
                $hours,
                $district,
                $locationName,
                $spot->address,
                $spot->phone,
                $spot->stars,
                $spot->likes,
                $spot->views,
                $spot->rec,
                $spot->ord
            ];
        }

        // 写入数据
        $sheet->fromArray($data, null, 'A2');

        // 设置推荐值和排序值列格式，确保显示0值
        $sheet->getStyle('L2:M' . (count($data) + 1))->getNumberFormat()->setFormatCode('0');

        // 自动调整列宽
        foreach (range('A', 'M') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // 创建Excel写入器
        $writer = new Xlsx($spreadsheet);

        // 使用内存流保存Excel文件
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer->save($tempFile);

        // 读取文件内容
        $fileContent = file_get_contents($tempFile);

        // 删除临时文件
        @unlink($tempFile);

        // 使用Filesystem保存文件
        $disk->write($excelPath, $fileContent);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_EXPORT,
            OperationLog::MODULE_SPOT,
            "导出景点数据",
            0,
            'spot'
        );

        // 返回文件下载地址
        return json([
            'url' => '/uploads/' . $excelPath,
            'filename' => $excelFilename,
        ]);
    }

    /**
     * 验证景点数据
     * @return array 验证后的数据
     */
    private function validateSpotData()
    {
        $data = $this->validate([
            'name|景点名称' => 'require',
            'images|景点图片' => 'require',
            'hours|开放时间' => function ($value) {
                if (empty($value['start']) || empty($value['end'])) {
                    return '开放时间不能为空';
                }
                if ($value['start'] > $value['end']) {
                    return '开放开始时间不能小于结束时间';
                }
                return true;
            },
            'location|景点位置' => 'require',
            'address|详细地址' => 'require',
            'phone|联系电话' => 'string',
            'tickets|景区项目' => 'require',
        ]);

        $data['location']['district'] = get_district($data['location']['latitude'], $data['location']['longitude']);

        return $data;
    }
}
