import { Content, ModalButton, ModalForm, RequestButton, Space, Table, dayjs, useUser } from '@topthink/common';
import { Dropdown } from 'react-bootstrap';
import getImageUrl from '../../utils';

export const Component = () => {
    // 获取当前用户信息
    const [user] = useUser();

    // 检查用户是否有权限
    const isAdmin = user?.access_level === 60; // 管理员角色
    return <Content>
        <Table
            search
            toolBarRender={() => {
                return <Space>
                    <RequestButton
                        url={'eatery/export'}
                        method={'post'}
                        variant={'secondary'}
                        onSuccess={({ url }) => {
                            if (url) {
                                window.open(url, '_blank');
                            }
                        }}
                    >导出</RequestButton>
                </Space>;
            }}
            source={'eatery'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '商家名称',
                    dataIndex: 'name',
                    render({ record }) {
                        if (record.images && record.images.length > 0) {
                            return <Space>
                                <img className={'rounded'} src={getImageUrl(record.images[0])} width={30} height={30} />
                                {record.name}
                            </Space>;
                        }
                        return record.name;
                    }
                },
                {
                    title: '商家类型',
                    dataIndex: 'type',
                    width: 80,
                    align: 'center',
                    render({ value }) {
                        return value === 1 ? '美食' : '特产';
                    }
                },
                {
                    title: '区县',
                    dataIndex: ['location', 'district'],
                    width: 80
                },
                {
                    title: '地址',
                    dataIndex: 'address',
                    width: 250
                },
                {
                    title: '店主',
                    dataIndex: 'owner',
                    width: 200,
                    render({ value }) {
                        return `${value.nickname}(${value.mobile})`;
                    }
                },
                {
                    title: '收藏/点赞/浏览',
                    width: 120,
                    align: 'center',
                    render({ record }) {
                        return <span style={{ whiteSpace: 'nowrap' }}>
                            {record.stars} / {record.likes} / {record.views}
                        </span>;
                    }
                },
                {
                    title: '推荐值',
                    width: 80,
                    align: 'center',
                    dataIndex: 'rec',
                    render({ value, record, action }) {
                        // 只有管理员可以修改推荐值
                        if (!isAdmin) {
                            return value || 0;
                        }

                        return <ModalForm
                            text={value}
                            modalProps={{ header: '设置推荐值' }}
                            action={`eatery/${record.id}/rec`}
                            method={'put'}
                            onSuccess={action.reload}
                            formData={{ rec: value }}
                            schema={{
                                type: 'object',
                                properties: {
                                    rec: {
                                        type: 'number',
                                        title: '推荐值',
                                        description: '值越大越靠前',
                                    }
                                }
                            }}
                        />;
                    },
                },
                {
                    title: '排序值',
                    width: 80,
                    align: 'center',
                    dataIndex: 'ord',
                    render({ value, record, action }) {
                        // 只有管理员可以修改排序值
                        if (!isAdmin) {
                            return value || 0;
                        }

                        return <ModalForm
                            text={value}
                            modalProps={{ header: '设置排序值' }}
                            action={`eatery/${record.id}/ord`}
                            method={'put'}
                            onSuccess={action.reload}
                            formData={{ ord: value }}
                            schema={{
                                type: 'object',
                                properties: {
                                    ord: {
                                        type: 'number',
                                        title: '排序值',
                                        description: '值越大越靠前',
                                    }
                                }
                            }}
                        />;
                    },
                },
                {
                    title: '操作',
                    width: 80,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <Dropdown align={'end'}>
                                <Dropdown.Toggle
                                    className={'border-0 no-caret'}
                                    variant={'outline-secondary'}
                                ><i className='bi bi-three-dots-vertical' /></Dropdown.Toggle>
                                <Dropdown.Menu className={'shadow'} renderOnMount popperConfig={{ strategy: 'fixed' }}>
                                    <ModalForm
                                        buttonProps={{ as: Dropdown.Item }}
                                        text={'设置店主'}
                                        schema={{
                                            type: 'object',
                                            properties: {
                                                user_id: {
                                                    type: 'number',
                                                }
                                            }
                                        }}
                                        uiSchema={{
                                            user_id: {
                                                'ui:widget': 'typeahead',
                                                'ui:label': false,
                                                'ui:options': {
                                                    endpoint: 'user/search',
                                                    minLength: 0,
                                                }
                                            }
                                        }}
                                    />
                                    <ModalButton as={Dropdown.Item} text={'员工列表'} modalProps={{
                                        size: 'xl',
                                        footer: false
                                    }}>
                                        <Table
                                            card={false}
                                            toolBarRender={false}
                                            source={`/eatery/${record.id}/members`}
                                            columns={[
                                                {
                                                    title: '姓名',
                                                    dataIndex: 'nickname',
                                                },
                                                {
                                                    title: '手机号',
                                                    dataIndex: 'mobile',
                                                    width: 120,
                                                },
                                                {
                                                    title: '加入时间',
                                                    dataIndex: ['pivot', 'create_time'],
                                                    width: 180,
                                                    render({ value }) {
                                                        return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
                                                    }
                                                }
                                            ]}
                                        />
                                    </ModalButton>
                                    <ModalButton as={Dropdown.Item} text={'商品列表'} modalProps={{
                                        size: 'xl',
                                        footer: false
                                    }}>
                                        <Table
                                            card={false}
                                            toolBarRender={false}
                                            source={`/eatery/${record.id}/foods`}
                                            columns={[
                                                {
                                                    title: '名称',
                                                    dataIndex: 'name',
                                                    render({ record }) {
                                                        return <Space>
                                                            <img className={'rounded'} src={getImageUrl(record.cover)} width={30} height={30} />
                                                            {record.name}
                                                        </Space>;
                                                    }
                                                },
                                                {
                                                    title: '价格',
                                                    dataIndex: 'price',
                                                    width: 80
                                                }
                                            ]}
                                        />
                                    </ModalButton>
                                    <ModalButton as={Dropdown.Item} text={'预定记录'} modalProps={{
                                        size: 'xl',
                                        footer: false
                                    }}>
                                        <Table
                                            card={false}
                                            toolBarRender={false}
                                            source={`/eatery/${record.id}/reservations`}
                                            columns={[
                                                {
                                                    title: '用户',
                                                    dataIndex: ['user', 'nickname'],
                                                },
                                                {
                                                    title: '手机号',
                                                    dataIndex: ['user', 'mobile'],
                                                    width: 120,
                                                },
                                                {
                                                    title: '预定时间',
                                                    dataIndex: 'create_time',
                                                    width: 180,
                                                    render({ value }) {
                                                        return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
                                                    }
                                                },
                                                {
                                                    title: '状态',
                                                    dataIndex: 'status_text',
                                                    width: 80,
                                                }
                                            ]}
                                        />
                                    </ModalButton>
                                    <ModalButton as={Dropdown.Item} text={'核销记录'} modalProps={{
                                        size: 'xl',
                                        footer: false
                                    }}>
                                        <Table
                                            card={false}
                                            toolBarRender={false}
                                            source={`/eatery/${record.id}/coupons`}
                                            columns={[
                                                {
                                                    title: '消费券',
                                                    dataIndex: ['info', 'name'],
                                                },
                                                {
                                                    title: '金额',
                                                    dataIndex: ['info', 'amount'],
                                                    valueType: 'currency',
                                                    width: 80,
                                                },
                                                {
                                                    title: '用户',
                                                    dataIndex: ['user', 'nickname'],
                                                },
                                                {
                                                    title: '手机号',
                                                    dataIndex: ['user', 'mobile'],
                                                    width: 120,
                                                },
                                                {
                                                    title: '核销时间',
                                                    dataIndex: 'use_time',
                                                    width: 180,
                                                    render({ value }) {
                                                        return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
                                                    }
                                                }
                                            ]}
                                        />
                                    </ModalButton>
                                    <RequestButton
                                        as={Dropdown.Item}
                                        className={'text-danger'}
                                        url={`eatery/${record.id}/unpend`}
                                        method={'post'}
                                        onSuccess={action.reload}
                                        confirm={`确定要将商家 "${record.name}" 重新放入审核列表吗？`}
                                    >取消审核</RequestButton>
                                </Dropdown.Menu>
                            </Dropdown>
                        </Space>;
                    }
                }

            ]}
        />
    </Content>;
};
